{"name": "retro-notes-fullstack", "version": "1.0.0", "description": "A vintage-styled full-stack note-taking application with Flask backend and Next.js frontend", "scripts": {"start": "node scripts/start.js", "start:windows": "start-app.bat", "start:unix": "./start-app.sh", "stop": "node scripts/stop.js", "stop:windows": "stop-app.bat", "stop:unix": "./stop-app.sh", "dev": "npm run start", "install:all": "npm run install:backend && npm run install:frontend", "install:backend": "cd note-app-be && pip install -r requirements.txt", "install:frontend": "cd note-app-frontend && npm install", "build:frontend": "cd note-app-frontend && npm run build", "test:api": "node scripts/test-api.js", "clean": "node scripts/clean.js", "logs": "node scripts/show-logs.js"}, "keywords": ["notes", "retro", "vintage", "flask", "nextjs", "typescript", "fullstack", "note-taking"], "author": "Retro Notes Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/retro-notes.git"}, "engines": {"node": ">=18.0.0", "python": ">=3.8.0"}, "devDependencies": {"chalk": "^4.1.2", "cross-spawn": "^7.0.6"}}