#!/usr/bin/env node

const { spawn } = require('cross-spawn');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  blue: '\x1b[34m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

console.log();
log('blue', '========================================');
log('blue', '   🎵 RETRO NOTES - STARTING... 🎵');
log('blue', '========================================');
console.log();

// Check prerequisites
log('blue', '🔍 Checking prerequisites...');

// Check Node.js
try {
  const nodeVersion = spawn.sync('node', ['--version'], { encoding: 'utf8' });
  if (nodeVersion.error) throw new Error('Node.js not found');
} catch (error) {
  log('red', '❌ Node.js not found. Please install Node.js');
  process.exit(1);
}

// Check Python
let pythonCmd = 'python';
let pipCmd = 'pip';
try {
  const pythonVersion = spawn.sync('python', ['--version'], { encoding: 'utf8' });
  if (pythonVersion.error) {
    // Try python3
    const python3Version = spawn.sync('python3', ['--version'], { encoding: 'utf8' });
    if (python3Version.error) throw new Error('Python not found');
    pythonCmd = 'python3';
    pipCmd = 'pip3';
  }
} catch (error) {
  log('red', '❌ Python not found. Please install Python');
  process.exit(1);
}

log('green', '✅ Prerequisites OK');
console.log();

// Setup backend
log('blue', '🔧 Setting up backend...');
const backendDir = path.join(__dirname, '..', 'note-app-be');
const venvDir = path.join(backendDir, 'venv');

// Create virtual environment if it doesn't exist
if (!fs.existsSync(venvDir)) {
  console.log('Creating virtual environment...');
  const createVenv = spawn.sync(pythonCmd, ['-m', 'venv', 'venv'], {
    cwd: backendDir,
    stdio: 'inherit'
  });
  if (createVenv.error || createVenv.status !== 0) {
    log('red', '❌ Failed to create virtual environment');
    process.exit(1);
  }
}

// Install backend dependencies
const isWindows = process.platform === 'win32';
const activateScript = isWindows
  ? path.join(venvDir, 'Scripts', 'activate.bat')
  : path.join(venvDir, 'bin', 'activate');

const pipPath = isWindows
  ? path.join(venvDir, 'Scripts', 'pip.exe')
  : path.join(venvDir, 'bin', 'pip');

const installBackend = spawn.sync(pipPath, ['install', '-r', 'requirements.txt'], {
  cwd: backendDir,
  stdio: 'pipe'
});

if (installBackend.error || installBackend.status !== 0) {
  log('red', '❌ Failed to install backend dependencies');
  process.exit(1);
}

log('green', '✅ Backend ready');

// Setup frontend
log('blue', '🔧 Setting up frontend...');
const frontendDir = path.join(__dirname, '..', 'note-app-frontend');
const nodeModulesDir = path.join(frontendDir, 'node_modules');

if (!fs.existsSync(nodeModulesDir)) {
  console.log('Installing dependencies...');
  const installFrontend = spawn.sync('npm', ['install'], {
    cwd: frontendDir,
    stdio: 'pipe'
  });

  if (installFrontend.error || installFrontend.status !== 0) {
    log('red', '❌ Failed to install frontend dependencies');
    process.exit(1);
  }
}

log('green', '✅ Frontend ready');
console.log();

log('blue', '🚀 Starting servers...');
console.log();
log('green', '📍 Backend:  http://localhost:5000');
log('green', '📍 Frontend: http://localhost:3000');
console.log();
console.log('Press Ctrl+C to stop both servers');
console.log();

// Start backend
const pythonPath = isWindows
  ? path.join(venvDir, 'Scripts', 'python.exe')
  : path.join(venvDir, 'bin', 'python');

const backendProcess = spawn(pythonPath, ['app.py'], {
  cwd: backendDir,
  stdio: 'pipe'
});

if (backendProcess.error) {
  log('red', '❌ Failed to start backend');
  process.exit(1);
}

// Wait a moment for backend to start
setTimeout(() => {
  // Start frontend
  const frontendProcess = spawn('npm', ['run', 'dev'], {
    cwd: frontendDir,
    stdio: 'inherit'
  });

  if (frontendProcess.error) {
    log('red', '❌ Failed to start frontend');
    backendProcess.kill();
    process.exit(1);
  }

  // Handle cleanup
  process.on('SIGINT', () => {
    console.log();
    log('blue', '🛑 Stopping servers...');
    backendProcess.kill();
    frontendProcess.kill();
    log('green', '✅ Servers stopped');
    process.exit(0);
  });

  frontendProcess.on('exit', () => {
    backendProcess.kill();
    process.exit(0);
  });

}, 3000);
