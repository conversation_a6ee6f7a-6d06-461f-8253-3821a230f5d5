#!/usr/bin/env node

const { spawn } = require('cross-spawn');
const path = require('path');
const fs = require('fs');

// Determine platform and run appropriate script
const isWindows = process.platform === 'win32';
const scriptName = isWindows ? 'start-app.bat' : 'start-app.sh';
const scriptPath = path.join(__dirname, '..', scriptName);

console.log('🎵 Starting Retro Notes Application...');
console.log(`Platform: ${process.platform}`);
console.log(`Using script: ${scriptName}`);

// Check if script exists
if (!fs.existsSync(scriptPath)) {
  console.error(`❌ Script not found: ${scriptPath}`);
  process.exit(1);
}

// Make script executable on Unix systems
if (!isWindows) {
  try {
    fs.chmodSync(scriptPath, '755');
  } catch (error) {
    console.warn('⚠️  Could not make script executable:', error.message);
  }
}

// Run the appropriate startup script
const child = spawn(scriptPath, [], {
  stdio: 'inherit',
  shell: true,
  cwd: path.join(__dirname, '..')
});

child.on('error', (error) => {
  console.error('❌ Failed to start application:', error.message);
  process.exit(1);
});

child.on('exit', (code) => {
  if (code !== 0) {
    console.error(`❌ Application exited with code ${code}`);
    process.exit(code);
  }
});
